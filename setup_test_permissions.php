<?php
// Setup test permissions for Secretary role
include 'includes/session.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'Admin') {
    die('Only admin can run this setup script');
}

echo "<h2>Setting up Test Permissions</h2>";

try {
    // First, let's assign some permissions to Secretary role (role_id = 1)
    $secretary_permissions = [
        // Dashboard permissions
        ['role_id' => 1, 'module' => 'dashboard', 'permission_name' => 'view_dashboard'],
        // Residents permissions
        ['role_id' => 1, 'module' => 'residents', 'permission_name' => 'view_residents'],
        ['role_id' => 1, 'module' => 'residents', 'permission_name' => 'add_resident'],
        // Documents permissions
        ['role_id' => 1, 'module' => 'documents', 'permission_name' => 'view_documents'],
        ['role_id' => 1, 'module' => 'documents', 'permission_name' => 'issue_clearance'],
        // Complaints permissions
        ['role_id' => 1, 'module' => 'complaints', 'permission_name' => 'view_complaints'],
        ['role_id' => 1, 'module' => 'complaints', 'permission_name' => 'add_complaint'],
        // Bail Management permissions (as per memory requirements)
        ['role_id' => 1, 'module' => 'bail', 'permission_name' => 'view_bail'],
        ['role_id' => 1, 'module' => 'bail', 'permission_name' => 'manage_bail']
    ];
    
    // Insert permissions if they don't exist
    foreach ($secretary_permissions as $perm) {
        // Check if permission exists
        $stmt = $conn->prepare("SELECT permission_id FROM permissions WHERE role_id = ? AND module = ? AND permission_name = ?");
        $stmt->execute([$perm['role_id'], $perm['module'], $perm['permission_name']]);
        
        if ($stmt->rowCount() == 0) {
            // Insert permission
            $stmt = $conn->prepare("INSERT INTO permissions (role_id, module, permission_name, description, status) VALUES (?, ?, ?, ?, 'active')");
            $description = ucwords(str_replace('_', ' ', $perm['permission_name']));
            $stmt->execute([$perm['role_id'], $perm['module'], $perm['permission_name'], $description]);
            echo "Added permission: {$perm['permission_name']} for module {$perm['module']}<br>";
        }
    }
    
    // Now assign these permissions to Secretary role in user_permissions table
    $stmt = $conn->prepare("SELECT permission_id FROM permissions WHERE role_id = 1");
    $stmt->execute();
    $permission_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($permission_ids as $permission_id) {
        // Check if assignment exists
        $stmt = $conn->prepare("SELECT id FROM user_permissions WHERE role_id = 1 AND permission_id = ?");
        $stmt->execute([$permission_id]);
        
        if ($stmt->rowCount() == 0) {
            // Insert assignment
            $stmt = $conn->prepare("INSERT INTO user_permissions (role_id, permission_id) VALUES (1, ?)");
            $stmt->execute([$permission_id]);
            echo "Assigned permission ID $permission_id to Secretary role<br>";
        }
    }
    
    echo "<h3>Secretary Role Permissions Setup Complete!</h3>";
    
    // Show current assignments
    $stmt = $conn->query("
        SELECT p.module, p.permission_name, p.description
        FROM permissions p
        INNER JOIN user_permissions up ON p.permission_id = up.permission_id
        WHERE up.role_id = 1
        ORDER BY p.module, p.permission_name
    ");
    
    echo "<h4>Secretary Role Current Permissions:</h4>";
    echo "<table border='1'>";
    echo "<tr><th>Module</th><th>Permission</th><th>Description</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>{$row['module']}</td>";
        echo "<td>{$row['permission_name']}</td>";
        echo "<td>{$row['description']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

echo "<br><a href='test_permissions.php'>Test Permissions</a>";
echo "<br><a href='admin/permissions.php'>Manage Permissions</a>";
?>
