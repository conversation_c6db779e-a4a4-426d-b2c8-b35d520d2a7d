<!DOCTYPE html>
<html>
<head>
    <title>Real AJAX Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .result {
            background: #ffffff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .info { border-left: 4px solid #17a2b8; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🧪 Real AJAX Permission Test</h1>
    
    <div class="test-section">
        <h3>📝 Test Form</h3>
        <form id="testForm">
            <label>User Type:</label>
            <select name="user_type" required>
                <option value="Secretary">Secretary</option>
                <option value="Chairman">Chairman</option>
                <option value="Kagawad">Kagawad</option>
                <option value="Staff">Staff</option>
            </select>
            
            <label>Module:</label>
            <select name="module" required>
                <option value="bail">Bail Management</option>
                <option value="residents">Residents Management</option>
                <option value="documents">Document Services</option>
                <option value="complaints">Complaints & Blotter</option>
            </select>
            
            <label>Permission Name:</label>
            <select name="permission_name" required>
                <option value="view_bail">View Bail Records</option>
                <option value="add_bail">Add Bail Record</option>
                <option value="edit_bail">Edit Bail Record</option>
            </select>
            
            <label>Description:</label>
            <textarea name="description" rows="3">Test permission description</textarea>
            
            <button type="submit">🚀 Test AJAX Request</button>
            <button type="button" onclick="clearResults()">🧹 Clear Results</button>
        </form>
    </div>
    
    <div class="test-section">
        <h3>📊 Request Details</h3>
        <div id="requestDetails" class="result info">No request made yet...</div>
    </div>
    
    <div class="test-section">
        <h3>📡 Network Response</h3>
        <div id="networkResponse" class="result info">No response yet...</div>
    </div>
    
    <div class="test-section">
        <h3>🔍 Analysis</h3>
        <div id="analysis" class="result info">No analysis yet...</div>
    </div>

    <script>
    $('#testForm').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous results
        $('#requestDetails').removeClass('success error').addClass('info');
        $('#networkResponse').removeClass('success error').addClass('info');
        $('#analysis').removeClass('success error').addClass('info');
        
        // Prepare form data
        var formData = $(this).serialize() + '&add_permission=1&ajax=1';
        
        // Show request details
        $('#requestDetails').text('🚀 Sending AJAX request...\n\nURL: admin/permissions.php\nMethod: POST\nData: ' + formData);
        
        // Make AJAX request with detailed error handling
        $.ajax({
            url: 'admin/permissions.php',
            method: 'POST',
            data: formData,
            dataType: 'text', // Get raw response first
            timeout: 10000,
            beforeSend: function(xhr) {
                $('#networkResponse').text('⏳ Sending request...');
            }
        })
        .done(function(response, textStatus, xhr) {
            // Show raw response
            $('#networkResponse').removeClass('info error').addClass('success')
                .text('✅ Response received!\n\n' +
                      'Status: ' + xhr.status + ' ' + xhr.statusText + '\n' +
                      'Content-Type: ' + xhr.getResponseHeader('Content-Type') + '\n' +
                      'Content-Length: ' + response.length + ' bytes\n\n' +
                      'Raw Response:\n' + response);
            
            // Analyze response
            analyzeResponse(response);
        })
        .fail(function(xhr, textStatus, errorThrown) {
            $('#networkResponse').removeClass('info success').addClass('error')
                .text('❌ Request failed!\n\n' +
                      'Status: ' + xhr.status + ' ' + xhr.statusText + '\n' +
                      'Error: ' + textStatus + '\n' +
                      'Exception: ' + errorThrown + '\n\n' +
                      'Response Text:\n' + xhr.responseText);
            
            // Analyze error response
            analyzeResponse(xhr.responseText, true);
        });
    });
    
    function analyzeResponse(response, isError = false) {
        var analysis = '';
        
        if (isError) {
            analysis += '❌ Request failed\n\n';
        } else {
            analysis += '✅ Request completed\n\n';
        }
        
        analysis += 'Response Length: ' + response.length + ' bytes\n';
        
        if (response.length === 0) {
            analysis += '⚠️ Empty response\n';
        } else {
            // Check if it's JSON
            try {
                var jsonData = JSON.parse(response);
                analysis += '✅ Valid JSON response\n';
                analysis += 'Parsed JSON:\n' + JSON.stringify(jsonData, null, 2) + '\n';
                
                if (jsonData.success) {
                    analysis += '🎉 Success: ' + jsonData.message;
                } else {
                    analysis += '⚠️ Error: ' + jsonData.message;
                }
            } catch (e) {
                analysis += '❌ Invalid JSON response\n';
                analysis += 'JSON Parse Error: ' + e.message + '\n\n';
                
                // Check for common issues
                if (response.includes('<!DOCTYPE') || response.includes('<html')) {
                    analysis += '🚨 HTML detected in response!\n';
                }
                
                if (response.includes('Warning:') || response.includes('Notice:') || response.includes('Error:')) {
                    analysis += '🚨 PHP errors detected in response!\n';
                }
                
                if (response.includes('Fatal error:')) {
                    analysis += '🚨 PHP fatal error detected!\n';
                }
                
                // Show first 500 characters
                analysis += '\nFirst 500 characters:\n';
                analysis += response.substring(0, 500);
                
                if (response.length > 500) {
                    analysis += '\n... (truncated)';
                }
            }
        }
        
        $('#analysis').removeClass('info').addClass(isError ? 'error' : 'success').text(analysis);
    }
    
    function clearResults() {
        $('#requestDetails').removeClass('success error').addClass('info').text('No request made yet...');
        $('#networkResponse').removeClass('success error').addClass('info').text('No response yet...');
        $('#analysis').removeClass('success error').addClass('info').text('No analysis yet...');
    }
    </script>
</body>
</html>
