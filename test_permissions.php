<?php
// Test permissions system
include 'includes/session.php';
include 'includes/functions/permission_functions.php';

// Test user types and their role mappings
echo "<h2>Testing Permission System</h2>";

// Test role mapping
echo "<h3>Role ID Mapping:</h3>";
$user_types = ['Secretary', 'Chairman', 'Kagawad', 'Staff', 'Admin'];
foreach ($user_types as $user_type) {
    $role_id = getUserTypeRoleId($user_type);
    echo "$user_type => Role ID: " . ($role_id ?? 'null') . "<br>";
}

// Test current user permissions
echo "<h3>Current User Info:</h3>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
echo "User Type: " . ($_SESSION['user_type'] ?? 'Not set') . "<br>";

// Test module access
echo "<h3>Module Access Test:</h3>";
$modules = ['dashboard', 'residents', 'documents', 'complaints', 'officials'];
foreach ($modules as $module) {
    $can_access = canAccessModule($module, $conn);
    echo "Can access $module: " . ($can_access ? 'YES' : 'NO') . "<br>";
}

// Test specific permissions
echo "<h3>Specific Permission Test:</h3>";
$permissions = [
    ['view_dashboard', 'dashboard'],
    ['view_residents', 'residents'],
    ['add_resident', 'residents'],
    ['view_documents', 'documents'],
    ['issue_clearance', 'documents']
];

foreach ($permissions as $perm) {
    $has_perm = hasPermission($perm[0], $perm[1], $conn);
    echo "Has permission {$perm[0]} in {$perm[1]}: " . ($has_perm ? 'YES' : 'NO') . "<br>";
}

// Show accessible modules
echo "<h3>Accessible Modules:</h3>";
$accessible = getAccessibleModules($conn);
echo "Accessible modules: " . implode(', ', $accessible) . "<br>";

// Check database for permissions
echo "<h3>Database Check:</h3>";
try {
    // Check if tables exist
    $stmt = $conn->query("SHOW TABLES LIKE 'permissions'");
    echo "Permissions table exists: " . ($stmt->rowCount() > 0 ? 'YES' : 'NO') . "<br>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'user_permissions'");
    echo "User_permissions table exists: " . ($stmt->rowCount() > 0 ? 'YES' : 'NO') . "<br>";
    
    // Count permissions
    $stmt = $conn->query("SELECT COUNT(*) FROM permissions");
    echo "Total permissions in database: " . $stmt->fetchColumn() . "<br>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM user_permissions");
    echo "Total user permission assignments: " . $stmt->fetchColumn() . "<br>";
    
    // Show permissions by role
    $stmt = $conn->query("
        SELECT p.role_id, p.module, p.permission_name, 
               CASE WHEN up.permission_id IS NOT NULL THEN 'Assigned' ELSE 'Not Assigned' END as status
        FROM permissions p
        LEFT JOIN user_permissions up ON p.permission_id = up.permission_id
        ORDER BY p.role_id, p.module, p.permission_name
    ");
    
    echo "<h4>Permission Details:</h4>";
    echo "<table border='1'>";
    echo "<tr><th>Role ID</th><th>Module</th><th>Permission</th><th>Status</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>{$row['role_id']}</td>";
        echo "<td>{$row['module']}</td>";
        echo "<td>{$row['permission_name']}</td>";
        echo "<td>{$row['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}
?>
