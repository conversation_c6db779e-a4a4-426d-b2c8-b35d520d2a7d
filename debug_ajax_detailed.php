<?php
// Detailed AJAX debugging script
include 'includes/session.php';

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>🔍 Detailed AJAX Permission Adding Debug</h2>";

// Test 1: Check if we can access the permissions.php file
echo "<h3>📋 Test 1: Basic File Access</h3>";
if (file_exists('admin/permissions.php')) {
    echo "<p style='color: green;'>✅ permissions.php file exists</p>";
} else {
    echo "<p style='color: red;'>❌ permissions.php file not found</p>";
}

// Test 2: Simulate the exact AJAX request
echo "<h3>🧪 Test 2: Simulating AJAX Request</h3>";

// Set up the exact POST data that would be sent
$_POST = [
    'add_permission' => '1',
    'user_type' => 'Secretary',
    'module' => 'bail',
    'permission_name' => 'view_bail',
    'description' => 'View Bail Records',
    'ajax' => '1'
];
$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h4>POST Data being sent:</h4>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h4>Capturing response...</h4>";

// Capture all output
ob_start();
$error_occurred = false;

try {
    // Set error handler to catch any errors
    set_error_handler(function($severity, $message, $file, $line) {
        global $error_occurred;
        $error_occurred = true;
        echo "PHP Error: $message in $file on line $line\n";
    });
    
    // Include the permissions file
    include 'admin/permissions.php';
    
} catch (Exception $e) {
    $error_occurred = true;
    echo "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    $error_occurred = true;
    echo "Fatal Error: " . $e->getMessage() . "\n";
}

// Restore error handler
restore_error_handler();

$output = ob_get_clean();

echo "<h4>Raw Response:</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; font-family: monospace; white-space: pre-wrap;'>";
echo htmlspecialchars($output);
echo "</div>";

echo "<h4>Response Analysis:</h4>";
echo "<p><strong>Length:</strong> " . strlen($output) . " bytes</p>";
echo "<p><strong>Error Occurred:</strong> " . ($error_occurred ? 'Yes' : 'No') . "</p>";

if (empty($output)) {
    echo "<p style='color: orange;'>⚠️ Empty response - this might indicate the script exited without output</p>";
} else {
    // Try to decode as JSON
    $json_decoded = json_decode($output, true);
    $json_error = json_last_error();
    
    if ($json_error === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✅ Valid JSON response</p>";
        echo "<h4>Parsed JSON:</h4>";
        echo "<pre>" . json_encode($json_decoded, JSON_PRETTY_PRINT) . "</pre>";
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
        echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>";
        
        // Check for common issues
        if (strpos($output, '<!DOCTYPE') !== false || strpos($output, '<html') !== false) {
            echo "<p style='color: red;'>🚨 HTML detected in response!</p>";
        }
        
        if (strpos($output, 'Warning:') !== false || strpos($output, 'Notice:') !== false) {
            echo "<p style='color: red;'>🚨 PHP warnings/notices detected!</p>";
        }
        
        if (strpos($output, 'Fatal error:') !== false) {
            echo "<p style='color: red;'>🚨 PHP fatal error detected!</p>";
        }
        
        // Show first and last 200 characters
        echo "<h4>First 200 characters:</h4>";
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; font-family: monospace;'>";
        echo htmlspecialchars(substr($output, 0, 200));
        echo "</div>";
        
        if (strlen($output) > 400) {
            echo "<h4>Last 200 characters:</h4>";
            echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; font-family: monospace;'>";
            echo htmlspecialchars(substr($output, -200));
            echo "</div>";
        }
    }
}

// Test 3: Check database connection and table
echo "<h3>🗄️ Test 3: Database Check</h3>";
try {
    $stmt = $conn->query("SELECT COUNT(*) FROM permissions");
    $count = $stmt->fetchColumn();
    echo "<p style='color: green;'>✅ Database connection OK, permissions table has $count records</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 4: Check session and user
echo "<h3>👤 Test 4: Session Check</h3>";
echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";
echo "<p><strong>User Type:</strong> " . ($_SESSION['user_type'] ?? 'Not set') . "</p>";
echo "<p><strong>Is Admin:</strong> " . (($_SESSION['user_type'] ?? '') === 'Admin' ? 'Yes' : 'No') . "</p>";

// Test 5: Manual permission insert
echo "<h3>➕ Test 5: Manual Permission Insert</h3>";
try {
    $stmt = $conn->prepare("INSERT INTO permissions (role_id, module, permission_name, description) VALUES (?, ?, ?, ?)");
    $result = $stmt->execute([1, 'test_debug', 'test_permission_debug', 'Debug test permission']);
    
    if ($result) {
        $insert_id = $conn->lastInsertId();
        echo "<p style='color: green;'>✅ Manual insert successful, ID: $insert_id</p>";
        
        // Clean up
        $stmt = $conn->prepare("DELETE FROM permissions WHERE permission_id = ?");
        $stmt->execute([$insert_id]);
        echo "<p>🧹 Test record cleaned up</p>";
    } else {
        echo "<p style='color: red;'>❌ Manual insert failed</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Manual insert error: " . $e->getMessage() . "</p>";
}

// Check for any PHP errors that occurred
$last_error = error_get_last();
if ($last_error && $last_error['message']) {
    echo "<h3>🚨 PHP Error Log:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; color: #721c24;'>";
    echo "<strong>Message:</strong> " . $last_error['message'] . "<br>";
    echo "<strong>File:</strong> " . $last_error['file'] . "<br>";
    echo "<strong>Line:</strong> " . $last_error['line'] . "<br>";
    echo "<strong>Type:</strong> " . $last_error['type'];
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h2 {
    color: #007bff;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h3 {
    color: #495057;
    margin-top: 30px;
    background: white;
    padding: 10px;
    border-left: 4px solid #007bff;
}

h4 {
    color: #6c757d;
    margin-top: 20px;
}

pre {
    background: #ffffff;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    border: 1px solid #dee2e6;
    max-height: 300px;
}

p {
    margin: 10px 0;
}
</style>
