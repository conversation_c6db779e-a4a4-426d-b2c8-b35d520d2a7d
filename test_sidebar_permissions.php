<?php
// Test sidebar permissions for different user types
include 'includes/session.php';
include 'includes/functions/permission_functions.php';

echo "<h2>Testing Sidebar Permissions</h2>";

// Test different user types
$test_user_types = ['Admin', 'Secretary', 'Chairman', 'Kagawad', 'Staff'];

foreach ($test_user_types as $user_type) {
    echo "<h3>Testing for User Type: $user_type</h3>";
    
    // Temporarily set session user_type for testing
    $original_user_type = $_SESSION['user_type'] ?? null;
    $_SESSION['user_type'] = $user_type;
    
    // Test module access
    $modules = [
        'dashboard' => 'Dashboard',
        'officials' => 'User Management', 
        'residents' => 'Resident Management',
        'documents' => 'Document Services',
        'complaints' => 'Complaints & Blotter',
        'bail' => 'Bail Management',
        'social' => 'Social Services',
        'finance' => 'Financial Management',
        'properties' => 'Infrastructure',
        'health' => 'Health Services',
        'disaster' => 'Disaster Management',
        'governance' => 'Governance',
        'communication' => 'Communication',
        'reports' => 'Reports & Analytics'
    ];
    
    echo "<table border='1'>";
    echo "<tr><th>Module</th><th>Can Access</th><th>Should Show in Sidebar</th></tr>";
    
    foreach ($modules as $module => $display_name) {
        $can_access = canAccessModule($module, $conn);
        $should_show = $can_access ? 'YES' : 'NO';
        $color = $can_access ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>$display_name</td>";
        echo "<td style='color: $color'>$should_show</td>";
        echo "<td style='color: $color'>$should_show</td>";
        echo "</tr>";
    }
    
    echo "</table><br>";
    
    // Restore original user type
    if ($original_user_type !== null) {
        $_SESSION['user_type'] = $original_user_type;
    }
}

echo "<h3>Expected Results for Secretary:</h3>";
echo "Secretary should only see:<br>";
echo "- Dashboard (YES)<br>";
echo "- Resident Management (YES)<br>";
echo "- Document Services (YES)<br>";
echo "- Complaints & Blotter (YES)<br>";
echo "- Bail Management (YES)<br>";
echo "- All others should be NO<br>";

echo "<br><h3>Test Login Instructions:</h3>";
echo "1. <a href='logout.php'>Logout</a> from current session<br>";
echo "2. <a href='login.php'>Login</a> with secretary_test / password123<br>";
echo "3. Check if sidebar only shows the modules listed above<br>";
echo "4. <a href='admin/permissions.php'>Back to Permissions Management</a><br>";
?>
