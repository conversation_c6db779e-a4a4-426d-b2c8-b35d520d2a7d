<?php
// Create test Secretary user
include 'includes/session.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'Admin') {
    die('Only admin can run this setup script');
}

echo "<h2>Creating Test Secretary User</h2>";

try {
    // Check if test user already exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE username = 'secretary_test'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "Test Secretary user already exists!<br>";
    } else {
        // Create test Secretary user
        $username = 'secretary_test';
        $password = password_hash('password123', PASSWORD_DEFAULT);
        $user_type = 'Secretary';
        $email = '<EMAIL>';
        
        $stmt = $conn->prepare("INSERT INTO users (username, password, user_type, related_id, related_type, email, status, date_created) VALUES (?, ?, ?, 1, 'Official', ?, 'Active', NOW())");
        $stmt->execute([$username, $password, $user_type, $email]);
        
        echo "Created test Secretary user:<br>";
        echo "Username: secretary_test<br>";
        echo "Password: password123<br>";
        echo "User Type: Secretary<br>";
    }
    
    // Show login instructions
    echo "<h3>Test Instructions:</h3>";
    echo "1. <a href='logout.php'>Logout</a> from admin account<br>";
    echo "2. <a href='login.php'>Login</a> with secretary_test / password123<br>";
    echo "3. Check if sidebar only shows modules with assigned permissions<br>";
    echo "4. Secretary should only see: Dashboard, Resident Management, Document Services, Complaints & Blotter<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
